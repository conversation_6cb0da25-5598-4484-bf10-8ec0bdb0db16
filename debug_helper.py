#!/usr/bin/env python3
"""
Debug helper script for monitoring and troubleshooting the IBT property search sessions.
"""

import asyncio
import logging
import json
from pathlib import Path
from datetime import datetime, timedelta
import argparse
import sys

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def analyze_log_file(log_file_path: str):
    """Analyze the log file for patterns and issues"""
    print(f"\n=== Analyzing log file: {log_file_path} ===")
    
    if not Path(log_file_path).exists():
        print(f"Log file not found: {log_file_path}")
        return
    
    with open(log_file_path, 'r') as f:
        lines = f.readlines()
    
    # Statistics
    total_lines = len(lines)
    error_lines = [line for line in lines if 'ERROR' in line]
    warning_lines = [line for line in lines if 'WARNING' in line]
    timeout_lines = [line for line in lines if 'Timeout' in line or 'timeout' in line]
    click_failures = [line for line in lines if 'Failed to click next page' in line]
    intercept_issues = [line for line in lines if 'intercepts pointer events' in line]
    
    print(f"Total log lines: {total_lines}")
    print(f"Error lines: {len(error_lines)}")
    print(f"Warning lines: {len(warning_lines)}")
    print(f"Timeout issues: {len(timeout_lines)}")
    print(f"Click failures: {len(click_failures)}")
    print(f"Element interception issues: {len(intercept_issues)}")
    
    # Show recent errors
    if error_lines:
        print(f"\n=== Recent Errors (last 5) ===")
        for line in error_lines[-5:]:
            print(line.strip())
    
    # Show click failure patterns
    if click_failures:
        print(f"\n=== Click Failure Analysis ===")
        for line in click_failures[-3:]:
            print(line.strip())
    
    # Show interception issues
    if intercept_issues:
        print(f"\n=== Element Interception Issues ===")
        for line in intercept_issues[-3:]:
            print(line.strip())
    
    # Find where the script stopped
    if lines:
        last_line = lines[-1]
        print(f"\n=== Last Log Entry ===")
        print(last_line.strip())
        
        # Try to determine what was happening when it stopped
        recent_lines = lines[-20:]
        session_activities = {}
        
        for line in recent_lines:
            if '[Session' in line:
                # Extract session ID and activity
                try:
                    session_part = line.split('[Session')[1].split(']')[0].strip()
                    activity = line.split(']', 1)[1].strip() if ']' in line else "Unknown"
                    session_activities[session_part] = activity
                except:
                    continue
        
        if session_activities:
            print(f"\n=== Session Activities Before Stop ===")
            for session, activity in session_activities.items():
                print(f"Session {session}: {activity}")

def check_debug_files():
    """Check for debug screenshots and HTML files"""
    print(f"\n=== Debug Files Analysis ===")
    
    screenshot_dir = Path("debug_screenshots")
    html_dir = Path("debug_html")
    
    if screenshot_dir.exists():
        screenshots = list(screenshot_dir.glob("*.png"))
        print(f"Debug screenshots found: {len(screenshots)}")
        if screenshots:
            # Show most recent screenshots
            recent_screenshots = sorted(screenshots, key=lambda x: x.stat().st_mtime)[-5:]
            print("Recent screenshots:")
            for screenshot in recent_screenshots:
                mtime = datetime.fromtimestamp(screenshot.stat().st_mtime)
                print(f"  - {screenshot.name} ({mtime.strftime('%Y-%m-%d %H:%M:%S')})")
    else:
        print("No debug screenshots directory found")
    
    if html_dir.exists():
        html_files = list(html_dir.glob("*.html"))
        print(f"Debug HTML files found: {len(html_files)}")
        if html_files:
            # Show most recent HTML files
            recent_html = sorted(html_files, key=lambda x: x.stat().st_mtime)[-5:]
            print("Recent HTML files:")
            for html_file in recent_html:
                mtime = datetime.fromtimestamp(html_file.stat().st_mtime)
                print(f"  - {html_file.name} ({mtime.strftime('%Y-%m-%d %H:%M:%S')})")
    else:
        print("No debug HTML directory found")

def suggest_improvements():
    """Suggest debugging improvements based on analysis"""
    print(f"\n=== Debugging Suggestions ===")
    
    suggestions = [
        "1. Run with headless=False to see what's happening visually",
        "2. Add more wait time between page clicks (increase sleep duration)",
        "3. Check if the website has changed its pagination structure",
        "4. Try running with fewer concurrent sessions to reduce load",
        "5. Implement page refresh and retry logic for stuck sessions",
        "6. Add network condition monitoring (slow network might cause issues)",
        "7. Consider using different click strategies (JS click, coordinate click)",
        "8. Monitor memory usage - browser instances might be consuming too much memory"
    ]
    
    for suggestion in suggestions:
        print(suggestion)

def monitor_current_run():
    """Monitor a currently running session"""
    print(f"\n=== Monitoring Current Run ===")
    print("This would monitor active sessions if the script is running...")
    print("Check the main log file for real-time updates:")
    print("  tail -f ibt_search.log")
    print("\nLook for these patterns:")
    print("  - Heartbeat messages showing session progress")
    print("  - Stuck session warnings")
    print("  - Element interception errors")
    print("  - Timeout errors")

def main():
    parser = argparse.ArgumentParser(description="Debug helper for IBT property search")
    parser.add_argument("--log-file", default="ibt_search.log", help="Path to log file")
    parser.add_argument("--action", choices=["analyze", "debug-files", "suggestions", "monitor"], 
                       default="analyze", help="Action to perform")
    
    args = parser.parse_args()
    
    print("=== IBT Property Search Debug Helper ===")
    
    if args.action == "analyze":
        analyze_log_file(args.log_file)
    elif args.action == "debug-files":
        check_debug_files()
    elif args.action == "suggestions":
        suggest_improvements()
    elif args.action == "monitor":
        monitor_current_run()
    
    # Always show debug files and suggestions
    if args.action == "analyze":
        check_debug_files()
        suggest_improvements()

if __name__ == "__main__":
    main()
