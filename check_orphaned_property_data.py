#!/usr/bin/env python3
"""
check_orphaned_property_data.py

This script checks for records in the property_data table that don't have a 
corresponding entry in the buildings table.

Relationship:
- property_data.fol_id → buildings.extra_field_1

This is the opposite of check_missing_records.py which finds buildings records
without matching property_data records.
"""
import os
import sqlite3
import logging
import csv
from datetime import datetime
from tabulate import tabulate
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
log_filename = f'orphaned_property_data_check_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename=log_filename,
    filemode='w'
)

# Also log to console
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logging.getLogger().addHandler(console_handler)

logger = logging.getLogger(__name__)

def check_orphaned_property_data(db_path="extraction.db", export_csv=True, show_details=True):
    """
    Check for property_data records that don't have matching buildings records.
    
    Args:
        db_path: Path to the SQLite database
        export_csv: Whether to export results to CSV
        show_details: Whether to show detailed information about each orphaned record
    
    Returns:
        List of orphaned records
    """
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return []
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row  # This allows accessing columns by name
    
    logger.info("Checking for orphaned property_data records...")
    
    try:
        # Query to find property_data records that don't have a match in buildings
        cursor = conn.execute("""
            SELECT p.*
            FROM property_data p
            LEFT JOIN buildings b ON p.fol_id = b.extra_field_1
            WHERE b.extra_field_1 IS NULL
        """)
        
        orphaned_records = [dict(row) for row in cursor.fetchall()]
        
        logger.info(f"Found {len(orphaned_records)} property_data records without matching buildings record")
        
        if orphaned_records:
            # Get some statistics
            total_property_data = conn.execute("SELECT COUNT(*) FROM property_data").fetchone()[0]
            total_buildings = conn.execute("SELECT COUNT(*) FROM buildings").fetchone()[0]
            orphaned_percentage = (len(orphaned_records) / total_property_data) * 100
            
            print(f"\n{'='*80}")
            print(f"ORPHANED PROPERTY_DATA RECORDS SUMMARY")
            print(f"{'='*80}")
            print(f"Total property_data records: {total_property_data}")
            print(f"Total buildings records: {total_buildings}")
            print(f"Orphaned property_data records: {len(orphaned_records)}")
            print(f"Orphaned percentage: {orphaned_percentage:.1f}%")
            print(f"{'='*80}")
            
            if show_details:
                # Show detailed information about orphaned records
                print(f"\nDETAILED ORPHANED RECORDS:")
                print(f"{'-'*80}")
                
                # Prepare data for tabular display
                display_data = []
                for record in orphaned_records[:20]:  # Show first 20 for readability
                    display_data.append([
                        record.get('fol_id', 'N/A'),
                        record.get('street', 'N/A'),
                        record.get('house_number', 'N/A'),
                        record.get('owner_name', 'N/A')[:30] if record.get('owner_name') else 'N/A',
                        record.get('owner_email', 'N/A')[:30] if record.get('owner_email') else 'N/A',
                        record.get('status', 'N/A'),
                        record.get('last_updated', 'N/A')
                    ])
                
                headers = ['FOL-ID', 'Street', 'House #', 'Owner Name', 'Owner Email', 'Status', 'Last Updated']
                print(tabulate(display_data, headers=headers, tablefmt='grid', maxcolwidths=[None, 20, 8, 30, 30, 15, 19]))
                
                if len(orphaned_records) > 20:
                    print(f"\n... and {len(orphaned_records) - 20} more records")
            
            # Export to CSV if requested
            if export_csv:
                csv_filename = f'orphaned_property_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
                with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                    if orphaned_records:
                        fieldnames = orphaned_records[0].keys()
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(orphaned_records)
                        logger.info(f"Orphaned records exported to {csv_filename}")
                        print(f"\nDetailed data exported to: {csv_filename}")
        else:
            print(f"\n{'='*80}")
            print("✅ No orphaned property_data records found!")
            print("All property_data records have matching buildings records.")
            print(f"{'='*80}")
        
    except sqlite3.Error as e:
        logger.error(f"Database error: {e}")
        return []
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return []
    finally:
        conn.close()
    
    return orphaned_records

def analyze_orphaned_patterns(orphaned_records):
    """
    Analyze patterns in orphaned records to help understand why they might be orphaned.
    """
    if not orphaned_records:
        return
    
    print(f"\n{'='*80}")
    print("ORPHANED RECORDS ANALYSIS")
    print(f"{'='*80}")
    
    # Analyze by status
    status_counts = {}
    for record in orphaned_records:
        status = record.get('status', 'Unknown')
        status_counts[status] = status_counts.get(status, 0) + 1
    
    print("\nBy Status:")
    for status, count in sorted(status_counts.items()):
        print(f"  {status}: {count}")
    
    # Analyze by presence of owner data
    with_owner = sum(1 for r in orphaned_records if r.get('owner_name'))
    with_email = sum(1 for r in orphaned_records if r.get('owner_email'))
    
    print(f"\nData Completeness:")
    print(f"  Records with owner name: {with_owner}")
    print(f"  Records with owner email: {with_email}")
    print(f"  Records without owner data: {len(orphaned_records) - with_owner}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Check for orphaned property_data records')
    parser.add_argument('--db-path', default='extraction.db', help='Path to SQLite database')
    parser.add_argument('--no-csv', action='store_true', help='Skip CSV export')
    parser.add_argument('--no-details', action='store_true', help='Skip detailed display')
    parser.add_argument('--analyze', action='store_true', help='Include pattern analysis')
    
    args = parser.parse_args()
    
    logger.info("Starting orphaned property_data records check...")
    
    orphaned_records = check_orphaned_property_data(
        db_path=args.db_path,
        export_csv=not args.no_csv,
        show_details=not args.no_details
    )
    
    if args.analyze and orphaned_records:
        analyze_orphaned_patterns(orphaned_records)
    
    logger.info("Check completed.")
