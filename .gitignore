# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment variables (except template)
.env
!.env.template

# Database files (except template)
*.db
*.sqlite
*.sqlite3
!*.db.template

# Logs
*.log
*.log.*
ibt_search.log*

# Output directories
output/
analysis/
debug_html/
debug_screenshots/
exploration_protocols/

# Playwright
.user-data/
user_data/
.ms-playwright/

# Generated files
*.pdf
*.xlsx
*.xls
*.csv
*.png
*.jpg
*.jpeg
*.gif
*.html
*.txt
sync_diff_report.txt
missing_records_*.csv
missing_records_*.html
missing_records_*.log
airtable_sync_*.log

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk

# IDE
.idea/
.vscode/
*.swp
*.swo
.ropeproject/

# Cache
.ruff_cache/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# Virtual environments
.venv/
venv/
env/
ENV/

# Repomix
.repomix/
repomix-output.*

# Documentation
*.md
!README.md
!LICENSE

# Temporary files
*.tmp
*.temp
*~

# Python cache
*.pyc
__pycache__
.pytest_cache/
.coverage
.coverage.*

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/