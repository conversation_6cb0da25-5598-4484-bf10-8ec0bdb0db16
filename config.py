from dataclasses import dataclass, field
import os


@dataclass
class ProjectConfig:
    key: str
    name: str
    base_url: str
    area_label: str
    username: str
    password: str
    otp_secret: str
    selectors: dict[str, str] = field(default_factory=dict)  # optional per-project overrides


PROJECTS: dict[str, ProjectConfig] = {
    "bad-sooden": ProjectConfig(
        key="bad-sooden",
        name="Bad Sooden-Allendorf, Stadt",
        base_url=os.getenv("BAD_SOODEN_URL", "https://glasfaser.telekom.de/auftragnehmerportal-ui/order/ibtorder/search?a-cid=58222"),
        area_label="Bad Sooden-Allendorf, Stadt",
        username=os.getenv("BAD_SOODEN_USER", "<EMAIL>"),
        password=os.getenv("BAD_SOODEN_PASS", "c@zzvjkZAz8yZ.ciA4ih"),
        otp_secret=os.getenv("BAD_SOODEN_OTP", "otpauth://totp/Telekom:hakan%40ekerfiber.com?secret=PFIGKZL2OFRXO2JQGRDFASKIOVGWO5TQ&digits=8&algorithm=SHA512&issuer=Telekom&period=30"),
    ),
    "giessen-nord": ProjectConfig(
        key="giessen-nord",
        name="Gießen Nord, Innenstadt",
        base_url="https://glasfaser.telekom.de/auftragnehmerportal-ui/order/ibtorder/search?a-cid=59709",
        area_label="Gießen Nord, Innenstadt",
        username=os.getenv("GIESSEN_USER", "<EMAIL>"),
        password=os.getenv("GIESSEN_PASS", "YiqVeRQ_wMuEMv4rVshz"),
        otp_secret=os.getenv("GIESSEN_OTP", "otpauth://totp/Telekom:hakan.eker%40ekerfiber.com?secret=MVIUMNRYNVCEWMLHNNSGGSTUGBFWE3SH&digits=8&algorithm=SHA512&issuer=Telekom&period=30"),
    ),
}