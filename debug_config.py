#!/usr/bin/env python3
"""
Configuration settings for debugging the IBT property search script.
Modify these settings to control debugging behavior.
"""

# Debugging Configuration
DEBUG_CONFIG = {
    # Screenshot settings
    "capture_screenshots": True,
    "screenshot_on_error": True,
    "screenshot_on_success": False,
    "screenshot_before_click": True,
    
    # HTML capture settings
    "save_html_on_error": True,
    "save_html_on_stuck": True,
    
    # Session monitoring
    "session_timeout_minutes": 10,
    "max_stuck_threshold": 5,
    "heartbeat_interval_seconds": 30,
    
    # Click strategy settings
    "click_timeout_ms": 10000,
    "click_retry_strategies": [
        "normal_click",
        "parent_click", 
        "force_click",
        "js_click",
        "coordinate_click"
    ],
    
    # Wait times (in seconds)
    "page_load_wait": 3,
    "element_wait_timeout": 5000,
    "recovery_wait": 5,
    
    # Logging settings
    "detailed_element_logging": True,
    "log_intercepting_elements": True,
    "log_session_status_interval": 300,  # 5 minutes
    
    # Recovery settings
    "enable_auto_recovery": True,
    "max_recovery_attempts": 3,
    "recovery_page_refresh": True,
}

# Browser settings for debugging
BROWSER_DEBUG_CONFIG = {
    "headless": False,  # Set to False to see browser window
    "slow_mo": 1000,    # Slow down actions by 1 second for visibility
    "devtools": True,   # Open DevTools
    "viewport": {"width": 1920, "height": 1080},
    "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
}

# Pagination debugging settings
PAGINATION_DEBUG = {
    "log_pagination_state": True,
    "capture_pagination_screenshots": True,
    "detailed_click_logging": True,
    "check_for_overlays": True,
    "wait_for_stability": True,
    "stability_timeout_ms": 2000,
}

def get_debug_config():
    """Get the current debug configuration"""
    return DEBUG_CONFIG

def get_browser_debug_config():
    """Get browser-specific debug configuration"""
    return BROWSER_DEBUG_CONFIG

def get_pagination_debug_config():
    """Get pagination-specific debug configuration"""
    return PAGINATION_DEBUG

def update_config_for_debugging():
    """Update configuration for maximum debugging visibility"""
    DEBUG_CONFIG.update({
        "capture_screenshots": True,
        "screenshot_on_error": True,
        "screenshot_before_click": True,
        "save_html_on_error": True,
        "detailed_element_logging": True,
        "log_intercepting_elements": True,
        "enable_auto_recovery": True,
    })
    
    BROWSER_DEBUG_CONFIG.update({
        "headless": False,
        "slow_mo": 2000,
        "devtools": True,
    })
    
    PAGINATION_DEBUG.update({
        "log_pagination_state": True,
        "capture_pagination_screenshots": True,
        "detailed_click_logging": True,
        "check_for_overlays": True,
    })

def update_config_for_production():
    """Update configuration for production (minimal debugging)"""
    DEBUG_CONFIG.update({
        "capture_screenshots": False,
        "screenshot_on_error": True,
        "screenshot_before_click": False,
        "save_html_on_error": True,
        "detailed_element_logging": False,
        "log_intercepting_elements": False,
    })
    
    BROWSER_DEBUG_CONFIG.update({
        "headless": True,
        "slow_mo": 0,
        "devtools": False,
    })

if __name__ == "__main__":
    print("Debug Configuration:")
    print("===================")
    print(f"Screenshots enabled: {DEBUG_CONFIG['capture_screenshots']}")
    print(f"Browser headless: {BROWSER_DEBUG_CONFIG['headless']}")
    print(f"Session timeout: {DEBUG_CONFIG['session_timeout_minutes']} minutes")
    print(f"Click strategies: {DEBUG_CONFIG['click_retry_strategies']}")
