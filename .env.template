# Telekom scraping environment template

# Default project selection for CLI
# Used when --project is not provided to property_data.py
# Options: bad-sooden, giessen-nord
PROJECT=bad-sooden

# Global defaults (fallbacks). Prefer per-project variables below.
# TELEKOM_USERNAME=
# TELEKOM_PASSWORD=
# TELEKOM_OTP_SECRET=

# Bad Sooden-Allendorf project credentials
BAD_SOODEN_USER=
BAD_SOODEN_PASS=
BAD_SOODEN_OTP=
# Optional: Override base URL via env (if not using default in config.py)
# BAD_SOODEN_URL=https://glasfaser.telekom.de/auftragnehmerportal-ui/order/ibtorder/search?a-cid=58222

# Gießen Nord (Innenstadt [Giga Area]) project credentials
GIESSEN_USER=
GIESSEN_PASS=
GIESSEN_OTP=
# Base URL for Gießen Nord is set in config.py; override here only if necessary
# GIESSEN_URL=http://glasfaser.telekom.de/auftragnehmerportal-ui/home?a-cid=59709
